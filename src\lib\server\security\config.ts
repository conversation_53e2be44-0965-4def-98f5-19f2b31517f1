/**
 * Security configuration constants
 */

export const SECURITY_CONFIG = {
  // File upload limits
  FILE_UPLOAD: {
    MAX_SIZE: 20 * 1024 * 1024, // 20MB
    ALLOWED_PDF_TYPES: ['application/pdf'],
    ALLOWED_ARCHIVE_TYPES: [
      'application/zip',
      'application/x-zip-compressed',
      'application/x-rar-compressed',
      'application/vnd.rar',
      'application/x-7z-compressed'
    ],
    // Magic number signatures for file type validation
    MAGIC_NUMBERS: {
      'application/pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
      'application/zip': [0x50, 0x4B, 0x03, 0x04], // PK..
      'application/x-rar-compressed': [0x52, 0x61, 0x72, 0x21], // Rar!
      'application/x-7z-compressed': [0x37, 0x7A, 0xBC, 0xAF] // 7z..
    }
  },

  // Archive extraction security
  ARCHIVE_EXTRACTION: {
    MAX_TOTAL_SIZE: 100 * 1024 * 1024, // 100MB total extraction limit
    MAX_COMPRESSION_RATIO: 100, // Maximum compression ratio to detect zip bombs
    MAX_NESTED_DEPTH: 3, // Maximum nested archive depth
    MAX_FILES_PER_ARCHIVE: 1000, // Maximum number of files per archive
    MAX_SINGLE_FILE_SIZE: 10 * 1024 * 1024, // 10MB per individual file
    EXTRACTION_TIMEOUT: 30000, // 30 seconds timeout for extraction
    EXCLUDED_DIRECTORIES: [
      '.vs', 'bin', 'obj', 'node_modules', 'packages',
      '.git', '__macosx', 'debug', 'release',
      'properties', '.idea', '.vscode', 'build', 'dist',
      'target', 'out', 'generated', 'temp', 'tmp'
    ],
    SUPPORTED_EXTENSIONS: ['.pdf', '.cs']
  },

  // Rate limiting
  RATE_LIMITS: {
    CLAUDE_API: {
      WINDOW_MS: 60 * 1000, // 1 minute
      MAX_REQUESTS: 10 // 10 requests per minute per user
    },
    FILE_UPLOAD: {
      WINDOW_MS: 60 * 1000, // 1 minute
      MAX_REQUESTS: 20 // 20 uploads per minute per user
    },
    BATCH_PROCESS: {
      WINDOW_MS: 5 * 60 * 1000, // 5 minutes
      MAX_REQUESTS: 3 // 3 batch processes per 5 minutes per user
    },
    LOGIN_ATTEMPTS: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes
      MAX_REQUESTS: 5 // 5 login attempts per 15 minutes per IP
    }
  },

  // Session security
  SESSION: {
    DEFAULT_DURATION_HOURS: 3,
    RENEW_THRESHOLD_HOURS: 1,
    TOKEN_SIZE_BYTES: 24,
    CACHE_TTL_MS: 15 * 60 * 1000, // 15 minutes
    CLEANUP_INTERVAL_MS: 5 * 60 * 1000 // 5 minutes
  },

  // Password security
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 255,
    REQUIRE_UPPERCASE: false,
    REQUIRE_LOWERCASE: false,
    REQUIRE_NUMBERS: false,
    REQUIRE_SPECIAL_CHARS: false,
    ARGON2_CONFIG: {
      MEMORY_COST: 19456,
      TIME_COST: 2,
      OUTPUT_LENGTH: 32,
      PARALLELISM: 1,
      SALT_LENGTH: 16
    }
  },

  // Input validation
  INPUT_VALIDATION: {
    MAX_TEXT_LENGTH: 1000,
    MAX_DESCRIPTION_LENGTH: 5000,
    MAX_NAME_LENGTH: 100,
    MIN_NAME_LENGTH: 2,
    MAX_USERNAME_LENGTH: 31,
    MIN_USERNAME_LENGTH: 3,
    MAX_EMAIL_LENGTH: 255,
    MIN_EMAIL_LENGTH: 3
  },

  // API security
  API: {
    CLAUDE_API: {
      MAX_RETRIES: 3,
      RETRY_DELAY_MS: 1000,
      TIMEOUT_MS: 30000,
      MAX_IMAGE_SIZE: 1.15 * 1024 * 1024, // 1.15MB
      MAX_IMAGES_PER_BATCH: 20
    }
  },

  // File system security
  FILE_SYSTEM: {
    UPLOAD_DIR_PERMISSIONS: 0o755,
    FILE_PERMISSIONS: 0o644,
    TEMP_FILE_CLEANUP_INTERVAL: 60 * 60 * 1000, // 1 hour
    MAX_TEMP_FILE_AGE: 24 * 60 * 60 * 1000, // 24 hours
    QUARANTINE_SUSPICIOUS_FILES: true
  },

  // Security headers
  SECURITY_HEADERS: {
    CONTENT_SECURITY_POLICY: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'"],
      'style-src': ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      'img-src': ["'self'", "data:", "https:"],
      'font-src': ["'self'", "https://fonts.gstatic.com"],
      'connect-src': ["'self'", "https://api.pwnedpasswords.com"],
      'frame-ancestors': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'", "https://pdfbankas.kurkas.com", "https://*.kurkas.com"]
    },
    X_FRAME_OPTIONS: 'DENY',
    X_CONTENT_TYPE_OPTIONS: 'nosniff',
    X_XSS_PROTECTION: '1; mode=block',
    REFERRER_POLICY: 'strict-origin-when-cross-origin',
    PERMISSIONS_POLICY: 'camera=(), microphone=(), geolocation=()'
  },

  // Logging and monitoring
  LOGGING: {
    LOG_FAILED_LOGINS: true,
    LOG_FILE_UPLOADS: true,
    LOG_SUSPICIOUS_ACTIVITY: true,
    LOG_API_CALLS: true,
    MAX_LOG_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    LOG_RETENTION_DAYS: 30
  },

  // Error handling
  ERROR_HANDLING: {
    EXPOSE_STACK_TRACES: false,
    GENERIC_ERROR_MESSAGE: 'An error occurred while processing your request',
    LOG_ALL_ERRORS: true,
    SANITIZE_ERROR_MESSAGES: true
  }
};

// Type for the security configuration
export type SecurityConfig = typeof SECURITY_CONFIG;

// Environment-specific overrides
export function getSecurityConfig(): SecurityConfig {
  // Create a deep copy of the configuration
  const config: SecurityConfig = JSON.parse(JSON.stringify(SECURITY_CONFIG));

  // HIBP configuration - conditionally add API domain to CSP
  if (process.env.HIBP_ENABLED !== 'true') {
    // Remove HIBP API from connect-src if HIBP is disabled
    config.SECURITY_HEADERS.CONTENT_SECURITY_POLICY['connect-src'] =
      config.SECURITY_HEADERS.CONTENT_SECURITY_POLICY['connect-src'].filter(
        src => src !== 'https://api.pwnedpasswords.com'
      );
  }

  // Development environment adjustments
  if (process.env.NODE_ENV === 'development') {
    config.ERROR_HANDLING.EXPOSE_STACK_TRACES = true;
    config.LOGGING.LOG_API_CALLS = false; // Reduce noise in development
  }

  // Production environment adjustments
  if (process.env.NODE_ENV === 'production') {
    config.SESSION.DEFAULT_DURATION_HOURS = 2; // Shorter sessions in production
    config.RATE_LIMITS.CLAUDE_API.MAX_REQUESTS = 5; // Stricter rate limiting
    config.PASSWORD.REQUIRE_UPPERCASE = true;
    config.PASSWORD.REQUIRE_NUMBERS = true;
  }

  return config;
}

export default SECURITY_CONFIG;
